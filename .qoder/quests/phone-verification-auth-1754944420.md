# 手机号验证码登录功能设计文档

## 概述

本文档设计一个手机号+验证码登录功能，当手机号未注册时自动注册新账号。该功能基于mall项目现有的Spring Security + JWT认证架构，遵循项目的代码规范和安全标准。

## 需求分析

### 功能需求
1. **手机号验证码登录**：用户输入手机号和验证码进行登录
2. **自动注册**：当手机号未注册时，自动创建新用户账号
3. **验证码管理**：发送、存储、验证手机验证码
4. **安全防护**：防刷机制、频率限制、验证码安全

### 非功能需求
1. **性能**：验证码验证响应时间 < 200ms
2. **安全**：验证码5分钟有效期，防止暴力破解
3. **可用性**：支持并发登录，Redis缓存提升性能
4. **可维护性**：遵循项目统一的API响应格式和异常处理

## 架构设计

### 整体架构
基于现有的mall项目架构，新增手机号验证码登录功能：

```mermaid
graph TB
    subgraph "客户端层"
        A[移动端App/Web]
    end
    
    subgraph "控制层"
        B[UmsMemberController]
    end
    
    subgraph "服务层"
        C[UmsMemberService]
        D[SmsService]
        E[UmsMemberCacheService]
    end
    
    subgraph "安全层"
        F[JwtTokenUtil]
        G[PasswordEncoder]
    end
    
    subgraph "数据层"
        H[Redis缓存]
        I[MySQL数据库]
        J[短信服务商]
    end
    
    A --> B
    B --> C
    C --> D
    C --> E
    C --> F
    C --> G
    E --> H
    C --> I
    D --> J
```

### 核心组件

#### 1. Controller层扩展
- 新增手机号验证码登录接口
- 新增手机号获取验证码接口
- 遵循CommonResult统一响应格式

#### 2. Service层扩展
- UmsMemberService新增手机号登录方法
- 新增SmsService处理短信发送
- 增强验证码管理逻辑

#### 3. 安全机制
- 基于现有JWT认证体系
- Redis缓存验证码和用户信息
- 防刷和频率限制机制

## 数据模型

### 现有用户模型分析
基于UmsMember模型，已包含手机号字段：

```java
public class UmsMember {
    private Long id;
    private String username;
    private String password;
    private String phone;  // 手机号字段
    private Integer status; // 账户状态
    private Date createTime;
    // ... 其他字段
}
```

### 验证码缓存结构
```
Redis Key: mall:authCode:{phone}
Value: {code}
TTL: 300秒（5分钟）
```

## API设计

### 1. 获取手机验证码

**接口地址**：`GET /sso/getPhoneAuthCode`

**请求参数**：
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| telephone | String | 是 | 手机号 |

**响应示例**：
```json
{
    "code": 200,
    "message": "验证码发送成功",
    "data": null
}
```

### 2. 手机号验证码登录

**接口地址**：`POST /sso/phoneLogin`

**请求参数**：
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| telephone | String | 是 | 手机号 |
| authCode | String | 是 | 验证码 |

**响应示例**：
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "token": "eyJhbGciOiJIUzUxMiJ9...",
        "tokenHead": "Bearer ",
        "member": {
            "id": 1,
            "username": "13800138000",
            "phone": "13800138000",
            "status": 1
        }
    }
}
```

## 核心流程设计

### 手机号验证码登录流程

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Controller as UmsMemberController
    participant Service as UmsMemberService
    participant Cache as UmsMemberCacheService
    participant SMS as SmsService
    participant DB as 数据库
    participant JWT as JwtTokenUtil

    %% 获取验证码流程
    Client->>Controller: GET /sso/getPhoneAuthCode
    Controller->>Service: generatePhoneAuthCode(telephone)
    Service->>Service: 校验手机号格式
    Service->>Service: 检查发送频率限制
    Service->>Service: 生成6位随机验证码
    Service->>SMS: 发送短信验证码
    Service->>Cache: setAuthCode(telephone, code, 300s)
    Service-->>Controller: 返回成功
    Controller-->>Client: 返回发送成功

    %% 验证码登录流程
    Client->>Controller: POST /sso/phoneLogin
    Controller->>Service: loginByPhone(telephone, authCode)
    Service->>Cache: getAuthCode(telephone)
    alt 验证码正确
        Service->>DB: 根据手机号查询用户
        alt 用户存在
            Service->>Service: 加载用户信息
        else 用户不存在
            Service->>Service: 自动注册新用户
            Service->>DB: 插入新用户记录
        end
        Service->>JWT: generateToken(userDetails)
        Service-->>Controller: 返回Token和用户信息
        Controller-->>Client: 返回登录成功
    else 验证码错误
        Service-->>Controller: 抛出验证失败异常
        Controller-->>Client: 返回验证码错误
    end
```

### 自动注册流程详解

```mermaid
flowchart TD
    A[手机号登录] --> B{验证码是否正确?}
    B -->|否| C[返回验证码错误]
    B -->|是| D{用户是否存在?}
    D -->|是| E[直接登录]
    D -->|否| F[自动注册新用户]
    F --> G[设置用户名为手机号]
    F --> H[设置默认密码]
    F --> I[设置默认会员等级]
    F --> J[设置账户状态为启用]
    F --> K[保存到数据库]
    K --> L[生成JWT Token]
    E --> L
    L --> M[返回登录成功]
```

## 安全机制设计

### 1. 验证码安全
- **有效期**：5分钟
- **长度**：6位数字
- **存储**：Redis缓存，避免数据库压力
- **一次性**：验证成功后立即删除

### 2. 防刷机制
- **发送频率限制**：同一手机号60秒内只能发送1次
- **IP限制**：同一IP每小时最多获取验证码20次
- **日限制**：同一手机号每天最多获取验证码10次
- **失败次数限制**：连续验证失败5次后锁定10分钟

### 3. 数据安全
- **手机号脱敏**：日志中手机号中间4位显示为*
- **验证码不返回**：获取验证码接口不返回验证码内容
- **HTTPS传输**：敏感数据必须通过HTTPS传输

### 4. 防刷机制实现

```mermaid
flowchart TD
    A[请求验证码] --> B{检查IP限制}
    B -->|超限| C[返回请求过于频繁]
    B -->|通过| D{检查手机号频率限制}
    D -->|超限| E[返回发送太频繁]
    D -->|通过| F{检查日发送限制}
    F -->|超限| G[返回今日发送次数用尽]
    F -->|通过| H[发送验证码]
    H --> I[更新各种计数器]
```

## 错误处理设计

### 异常分类和处理

| 异常类型 | HTTP状态码 | 错误码 | 错误信息 |
|----------|------------|--------|----------|
| 手机号格式错误 | 400 | 400 | 手机号格式不正确 |
| 验证码为空 | 400 | 400 | 验证码不能为空 |
| 验证码错误 | 400 | 400 | 验证码错误或已过期 |
| 发送频率限制 | 429 | 429 | 验证码发送过于频繁，请稍后再试 |
| 短信发送失败 | 500 | 500 | 短信发送失败，请稍后重试 |
| 系统异常 | 500 | 500 | 系统异常，请联系客服 |

### 全局异常处理
基于项目现有的GlobalExceptionHandler进行扩展：

```java
@ExceptionHandler(SmsException.class)
public CommonResult handleSmsException(SmsException e) {
    return CommonResult.failed(e.getMessage());
}

@ExceptionHandler(AuthCodeException.class) 
public CommonResult handleAuthCodeException(AuthCodeException e) {
    return CommonResult.validateFailed(e.getMessage());
}
```

## 测试策略

### 1. 单元测试

#### 测试范围
- **Service层**：验证码生成、验证、用户自动注册逻辑
- **Cache层**：验证码存储和获取
- **Util层**：手机号格式验证、验证码生成算法

#### 关键测试用例
1. **验证码生成测试**
   - 生成6位数字验证码
   - 验证码唯一性测试
   
2. **验证码验证测试**
   - 正确验证码验证通过
   - 错误验证码验证失败
   - 过期验证码验证失败
   
3. **自动注册测试**
   - 新手机号自动注册成功
   - 已存在手机号直接登录
   - 注册用户信息正确性验证

### 2. 集成测试

#### 测试场景
1. **完整登录流程测试**
   - 获取验证码 → 验证码登录 → 获取用户信息
   
2. **异常情况测试**
   - 网络异常处理
   - 短信服务异常处理
   - Redis异常处理

### 3. 性能测试

#### 性能指标
- **验证码发送**：响应时间 < 3秒
- **验证码验证**：响应时间 < 200ms
- **并发登录**：支持1000并发用户登录

#### 测试方法
- 使用JMeter进行压力测试
- 监控Redis和数据库性能
- 分析接口响应时间分布

### 4. 安全测试

#### 测试内容
1. **防刷测试**：验证频率限制机制
2. **验证码暴力破解测试**：6位数字组合暴力测试
3. **重放攻击测试**：验证码一次性使用验证
4. **SQL注入测试**：手机号参数安全性测试

## 部署和监控

### 1. 配置管理

#### 新增配置项
```yaml
# 短信服务配置
sms:
  provider: aliyun # 短信服务商
  accessKeyId: ${SMS_ACCESS_KEY_ID}
  accessKeySecret: ${SMS_ACCESS_KEY_SECRET}
  signName: 商城系统
  templateCode: SMS_123456789

# 验证码配置  
auth-code:
  length: 6
  expire: 300 # 5分钟
  send-interval: 60 # 发送间隔60秒
  daily-limit: 10 # 每日限制
  hour-limit: 20 # 每小时限制
```

### 2. 监控指标

#### 业务监控
- **验证码发送成功率**：目标 > 99%
- **验证码验证成功率**：监控异常验证率
- **自动注册用户数**：统计新用户增长
- **登录响应时间**：P95 < 500ms

#### 技术监控
- **Redis连接数**：监控缓存服务状态
- **短信接口调用量**：监控第三方服务使用情况
- **异常告警**：发送失败、验证失败等异常监控

### 3. 日志设计

#### 日志级别和内容
```java
// INFO级别 - 正常业务流程
log.info("手机号验证码发送成功, phone: {}", PhoneUtil.mask(phone));
log.info("用户自动注册成功, phone: {}, userId: {}", PhoneUtil.mask(phone), userId);

// WARN级别 - 异常情况
log.warn("验证码验证失败, phone: {}, code: {}", PhoneUtil.mask(phone), "****");
log.warn("发送频率超限, phone: {}, ip: {}", PhoneUtil.mask(phone), clientIp);

// ERROR级别 - 系统错误
log.error("短信发送失败, phone: {}, error: {}", PhoneUtil.mask(phone), e.getMessage());
```

### 4. 灰度发布策略

#### 发布阶段
1. **内测阶段**：仅开发和测试人员可用
2. **灰度阶段**：5%真实用户流量
3. **扩量阶段**：逐步扩大到50%流量  
4. **全量阶段**：100%用户流量

#### 风险控制
- 提供功能开关，可快速关闭新功能
- 保留原有用户名密码登录方式
- 监控新功能异常率，超阈值自动降级

## 总结

本设计文档基于mall项目现有架构，设计了完整的手机号验证码登录功能。主要特点：

1. **架构兼容**：基于现有Spring Security + JWT架构，无缝集成
2. **安全可靠**：多层次防护机制，确保系统安全
3. **性能优化**：Redis缓存提升性能，支持高并发
4. **易于维护**：遵循项目代码规范，统一异常处理和响应格式
5. **可扩展性**：预留接口扩展空间，支持多种短信服务商

该功能的实现将为用户提供更便捷的登录体验，同时保证系统的安全性和稳定性。