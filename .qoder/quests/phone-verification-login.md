# 手机号验证码登录与自动注册功能设计

## 概述

基于现有mall项目的Spring Security + JWT认证体系，扩展实现手机号验证码登录功能。当手机号未注册时自动创建账户，提供便捷的免密登录体验。

## 架构设计

### 系统架构流程

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Controller as UmsMemberController
    participant Service as UmsMemberService
    participant C<PERSON> as Redis缓存
    participant DB as MySQL数据库
    participant JWT as JWT工具

    Client->>Controller: 1. 获取验证码(手机号)
    Controller->>Service: generateAuthCode()
    Service->>Cache: 存储验证码(90秒有效期)
    Service-->>Client: 验证码发送成功

    Client->>Controller: 2. 手机号验证码登录
    Controller->>Service: loginByPhone(phone, code)
    Service->>Cache: 验证码校验
    alt 验证码正确
        Service->>DB: 查询用户(通过手机号)
        alt 用户存在
            Service->>JWT: 生成Token
        else 用户不存在
            Service->>DB: 自动注册新用户
            Service->>JWT: 生成Token
        end
        Service-->>Client: 返回Token和用户信息
    else 验证码错误
        Service-->>Client: 验证失败
    end
```

### 核心组件扩展

```mermaid
classDiagram
    class UmsMemberService {
        +String loginByPhone(phone, authCode)
        +UmsMember getByPhone(phone)
        -UmsMember autoRegisterByPhone(phone)
        -String generateRandomPassword()
    }

    class UmsMemberController {
        +CommonResult phoneLogin(phone, authCode)
        +CommonResult getPhoneAuthCode(phone)
    }

    class UmsMember {
        +String phone
        +Integer sourceType
        +String password
        +Integer status
    }

    UmsMemberController --> UmsMemberService
    UmsMemberService --> UmsMember
```

## 技术实现

### API接口设计

#### 1. 获取验证码接口
- **路径**: `GET /sso/getPhoneAuthCode`
- **参数**: `phone` (手机号)
- **功能**: 生成6位数字验证码，存储到Redis，有效期90秒
- **返回**: 成功状态（实际验证码通过短信发送）

#### 2. 手机号验证码登录接口
- **路径**: `POST /sso/phoneLogin`
- **参数**: `phone` (手机号), `authCode` (验证码)
- **功能**: 验证码校验 → 用户查询/自动注册 → JWT令牌生成
- **返回**: JWT令牌和用户信息

### 核心实现逻辑

#### Service层扩展
```java
// 手机号验证码登录
String loginByPhone(String phone, String authCode);

// 根据手机号查询用户
UmsMember getByPhone(String phone);

// 自动注册（内部方法）
UmsMember autoRegisterByPhone(String phone);
```

#### 登录核心逻辑
1. **验证码校验** - 复用现有 `verifyAuthCode()` 方法
2. **用户查询** - 通过 `phone` 字段查询 `ums_member` 表
3. **自动注册策略** - 用户不存在时创建新账户：
   - 用户名设为手机号
   - 生成BCrypt加密的随机密码
   - 设置默认会员等级
   - 状态为启用(status=1)
   - 来源类型标记为手机号注册(source_type=2)
4. **JWT生成** - 使用现有 `jwtTokenUtil.generateToken()` 方法

### 安全机制

#### 验证码安全
- **有效期**: 90秒（继承现有配置）
- **长度**: 6位数字
- **存储**: Redis缓存，key格式 `mall:authCode:手机号`
- **防重复**: 同一手机号短时间内限制发送频率

#### 用户数据安全
- **密码生成**: 12位随机字符串，包含字母数字特殊字符
- **密码加密**: BCrypt算法加密存储
- **账户状态**: 自动注册用户默认启用
- **数据完整性**: 手机号唯一索引确保无重复

#### JWT令牌安全
- **继承现有机制**: 7天有效期，HS512签名算法
- **令牌头**: `Bearer ` 前缀
- **用户信息**: 包含用户ID、用户名、权限等

## 测试策略

### 功能测试场景
1. **已注册用户登录** - 验证码正确时直接登录成功
2. **未注册用户登录** - 自动注册后登录成功
3. **验证码错误** - 返回验证失败提示
4. **验证码过期** - 超过90秒后验证失败
5. **手机号格式错误** - 参数验证失败
6. **JWT令牌生成** - 登录成功后返回有效令牌

### 安全测试场景
1. **验证码暴力破解** - 连续错误尝试的限制机制
2. **重放攻击防护** - 验证码一次性使用限制
3. **SQL注入防护** - 参数化查询确保数据安全
4. **手机号格式验证** - 正则表达式验证手机号合法性

### 性能测试
1. **并发登录** - 高并发场景下的系统稳定性
2. **Redis性能** - 验证码缓存的读写性能
3. **数据库查询** - 手机号索引查询效率
4. **JWT生成速度** - 令牌生成的响应时间