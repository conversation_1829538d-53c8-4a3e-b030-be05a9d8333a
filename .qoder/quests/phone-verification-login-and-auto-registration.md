# 手机号验证码登录与自动注册功能设计

## 概述

本设计实现基于手机号和短信验证码的用户登录功能，当手机号未注册时自动创建账户，提供便捷的一键登录体验。

## 架构设计

### 系统架构

系统基于现有的 Spring Security + JWT 认证体系，扩展手机号登录方式。核心组件包括：

```mermaid
graph TB
    A[客户端请求] --> B[UmsMemberController]
    B --> C[UmsMemberService]
    C --> D[验证码校验]
    D --> E{用户是否存在}
    E -->|否| F[自动注册]
    E -->|是| G[直接登录]
    F --> H[生成JWT Token]
    G --> H
    H --> I[返回登录结果]
    
    subgraph "存储层"
        J[Redis缓存 - 验证码]
        K[MySQL - 用户数据]
    end
    
    C --> J
    C --> K
```

### 核心流程

```mermaid
sequenceDiagram
    participant C as 客户端
    participant API as API服务
    participant Cache as Redis
    participant DB as 数据库
    
    C->>API: 1. 获取验证码
    API->>Cache: 2. 生成并缓存验证码
    API-->>C: 3. 返回成功
    
    C->>API: 4. 手机号验证码登录
    API->>Cache: 5. 校验验证码
    API->>DB: 6. 查询用户信息
    
    alt 用户不存在
        API->>DB: 7. 自动注册新用户
    end
    
    API->>API: 8. 生成JWT Token
    API-->>C: 9. 返回登录结果
```

## 技术实现

### API 接口

#### 1. 获取验证码接口
- **路径**: `GET /sso/getAuthCode`
- **参数**: telephone (手机号)
- **功能**: 生成6位数字验证码，存储到Redis，有效期90秒

#### 2. 手机号登录接口
- **路径**: `POST /sso/phoneLogin`
- **参数**: telephone, authCode
- **功能**: 验证码校验，用户查询/自动注册，生成JWT令牌

### 服务层扩展

在 `UmsMemberService` 接口中添加：
```java
// 手机号验证码登录
String loginByPhone(String telephone, String authCode);

// 根据手机号获取用户
UmsMember getByPhone(String telephone);
```

核心实现逻辑：
1. **验证码校验** - 使用现有的 `verifyAuthCode` 方法
2. **用户查询** - 通过手机号查询 `ums_member` 表
3. **自动注册** - 用户不存在时创建新账户
4. **JWT生成** - 使用现有的 `jwtTokenUtil.generateToken` 方法

### 自动注册策略

当手机号未注册时：
- 用户名设为手机号
- 生成随机密码（BCrypt加密）
- 设置默认会员等级
- 账户状态为启用（status=1）
- 来源类型标记为手机号注册（source_type=2）

## 安全机制

- **验证码有效期**: 90秒（可配置）
- **验证码长度**: 6位数字
- **防重复验证**: 同一手机号验证失败后有频率限制
- **JWT安全**: 继承现有的令牌机制和过期策略
- **密码安全**: 自动生成的密码经过BCrypt加密存储

## 测试策略

### 功能测试
1. 验证码生成和校验
2. 已注册用户登录流程
3. 未注册用户自动注册流程
4. JWT令牌生成和验证
5. 异常场景处理（验证码错误、过期等）

### 安全测试
1. 验证码暴力破解防护
2. 手机号格式验证
3. 重放攻击防护
4. SQL注入防护