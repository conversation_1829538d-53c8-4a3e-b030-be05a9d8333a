# 手机号+验证码登录功能技术设计文档

## 1. 功能概述

实现基于手机号码和短信验证码的用户登录功能，当手机号码未注册时自动创建账户，提供便捷的一键登录体验。

## 2. 现有系统分析

### 2.1 当前用户认证架构
- 基于 Spring Security + JWT 的认证体系
- 使用 `UmsMemberService` 管理用户相关操作
- 支持用户名+密码登录方式
- 已有验证码生成和校验机制(`generateAuthCode`, `verifyAuthCode`)
- Redis 缓存验证码，默认过期时间可配置

### 2.2 数据库表结构
`ums_member` 表已包含必要字段：
- `phone`: 手机号码字段，已设置唯一索引
- `username`: 用户名字段，已设置唯一索引
- `password`: 密码字段（手机号登录时可设置为随机值）
- `member_level_id`: 会员等级，新用户默认为普通会员
- `status`: 账户状态，新用户默认启用(1)
- `create_time`: 注册时间

## 3. 技术设计方案

### 3.1 业务流程设计

#### 3.1.1 手机号验证码登录流程
```mermaid
sequenceDiagram
    participant Client as 客户端
    participant API as API服务
    participant SMS as 短信服务
    participant Cache as Redis缓存
    participant DB as 数据库

    Client->>API: 1. 获取验证码(手机号)
    API->>DB: 2. 检查手机号是否存在
    API->>SMS: 3. 发送短信验证码
    API->>Cache: 4. 缓存验证码(5分钟)
    API-->>Client: 5. 返回发送成功

    Client->>API: 6. 手机号验证码登录
    API->>Cache: 7. 验证验证码
    alt 验证码正确
        API->>DB: 8. 查询用户信息
        alt 用户不存在
            API->>DB: 9. 自动注册新用户
        end
        API->>API: 10. 生成JWT Token
        API-->>Client: 11. 返回Token和用户信息
    else 验证码错误
        API-->>Client: 12. 返回验证失败
    end
```

#### 3.1.2 自动注册逻辑
当手机号不存在时，系统将：
1. 以手机号作为用户名创建新账户
2. 生成随机密码（用户后续可通过验证码修改）
3. 设置默认会员等级
4. 设置账户状态为启用
5. 记录注册时间和来源类型

### 3.2 API 接口设计

#### 3.2.1 获取手机验证码接口
```http
GET /sso/getPhoneAuthCode?telephone={phone}
```

**请求参数：**
- `telephone`: 手机号码(必填)

**响应格式：**
```json
{
  "code": 200,
  "message": "获取验证码成功",
  "data": null
}
```

#### 3.2.2 手机号验证码登录接口
```http
POST /sso/phoneLogin
Content-Type: application/json

{
  "telephone": "13800138000",
  "authCode": "123456"
}
```

**请求参数：**
- `telephone`: 手机号码(必填)
- `authCode`: 验证码(必填)

**响应格式：**
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "token": "eyJhbGciOiJIUzUxMiJ9...",
    "tokenHead": "Bearer ",
    "userInfo": {
      "id": 12,
      "username": "13800138000",
      "phone": "13800138000",
      "nickname": null,
      "memberLevelId": 4,
      "status": 1,
      "isNewUser": true
    }
  }
}
```

### 3.3 核心代码实现

#### 3.3.1 Service 层接口扩展
在 `UmsMemberService` 中添加新方法：

```java
/**
 * 手机号验证码登录，不存在则自动注册
 */
String loginByPhone(String telephone, String authCode);

/**
 * 根据手机号获取用户
 */
UmsMember getByPhone(String telephone);
```

#### 3.3.2 Service 层实现逻辑
```java
@Override
public String loginByPhone(String telephone, String authCode) {
    // 1. 验证验证码
    if (!verifyAuthCode(authCode, telephone)) {
        Asserts.fail("验证码错误");
    }
    
    // 2. 查找用户
    UmsMember member = getByPhone(telephone);
    
    // 3. 用户不存在则自动注册
    if (member == null) {
        member = registerByPhone(telephone);
    }
    
    // 4. 生成 Token
    UserDetails userDetails = new MemberUserDetails(member);
    return jwtTokenUtil.generateToken(userDetails);
}

private UmsMember registerByPhone(String telephone) {
    UmsMember member = new UmsMember();
    member.setUsername(telephone);
    member.setPhone(telephone);
    member.setPassword(passwordEncoder.encode(generateRandomPassword()));
    member.setStatus(1);
    member.setCreateTime(new Date());
    member.setSourceType(2); // 手机号注册来源
    
    // 设置默认会员等级
    UmsMemberLevelExample levelExample = new UmsMemberLevelExample();
    levelExample.createCriteria().andDefaultStatusEqualTo(1);
    List<UmsMemberLevel> memberLevelList = memberLevelMapper.selectByExample(levelExample);
    if (!CollectionUtils.isEmpty(memberLevelList)) {
        member.setMemberLevelId(memberLevelList.get(0).getId());
    }
    
    memberMapper.insert(member);
    return member;
}
```

#### 3.3.3 Controller 层实现
```java
@ApiOperation("手机号验证码登录")
@RequestMapping(value = "/phoneLogin", method = RequestMethod.POST)
@ResponseBody
public CommonResult phoneLogin(@RequestParam String telephone,
                              @RequestParam String authCode) {
    String token = memberService.loginByPhone(telephone, authCode);
    if (token == null) {
        return CommonResult.failed("登录失败");
    }
    
    Map<String, Object> result = new HashMap<>();
    result.put("token", token);
    result.put("tokenHead", tokenHead);
    
    // 获取用户信息
    UmsMember member = memberService.getByPhone(telephone);
    result.put("userInfo", member);
    
    return CommonResult.success(result, "登录成功");
}

@ApiOperation("获取手机验证码")
@RequestMapping(value = "/getPhoneAuthCode", method = RequestMethod.GET)
@ResponseBody
public CommonResult getPhoneAuthCode(@RequestParam String telephone) {
    // 手机号格式验证
    if (!isValidPhoneNumber(telephone)) {
        return CommonResult.validateFailed("手机号格式不正确");
    }
    
    String authCode = memberService.generateAuthCode(telephone);
    return CommonResult.success(null, "获取验证码成功");
}
```

### 3.4 数据库变更

#### 3.4.1 用户来源类型扩展
在 `ums_member` 表的 `source_type` 字段中添加新的来源类型：
- 0: 未知
- 1: 用户名注册
- 2: 手机号注册
- 3: 第三方登录

#### 3.4.2 索引优化
确保 `phone` 字段的唯一索引存在：
```sql
ALTER TABLE ums_member ADD UNIQUE INDEX idx_phone(phone);
```

### 3.5 配置参数

#### 3.5.1 Redis 配置
```yaml
redis:
  key:
    authCode: "ums:authCode:"
  expire:
    authCode: 300 # 验证码过期时间(秒)
```

#### 3.5.2 短信服务配置
```yaml
sms:
  provider: "aliyun" # 短信服务提供商
  template:
    login: "SMS_123456789" # 登录验证码模板
  signName: "商城系统"
```

## 4. 安全考虑

### 4.1 验证码安全
- 验证码有效期：5分钟
- 同一手机号1分钟内只能发送一次验证码
- 验证码长度：6位数字
- 验证失败次数限制：同一手机号1小时内最多验证失败10次

### 4.2 防刷机制
- IP 限制：同一IP每小时最多获取验证码20次
- 手机号限制：同一手机号每天最多获取验证码10次
- 图形验证码：连续失败后要求输入图形验证码

### 4.3 用户数据安全
- 自动生成的密码使用强随机算法
- 敏感信息不记录在日志中
- 支持用户后续绑定邮箱等安全信息

## 5. 监控和日志

### 5.1 关键指标监控
- 验证码发送成功率
- 手机号登录成功率
- 自动注册用户数量
- 登录响应时间

### 5.2 日志记录
- 验证码发送记录
- 登录成功/失败记录
- 自动注册记录
- 异常情况记录

## 6. 测试计划

### 6.1 单元测试
- 验证码生成和校验逻辑
- 自动注册逻辑
- 手机号格式验证
- Token 生成逻辑

### 6.2 集成测试
- 完整登录流程测试
- 短信服务集成测试
- Redis 缓存功能测试
- 数据库操作测试

### 6.3 安全测试
- 验证码暴力破解测试
- 接口防刷测试
- SQL 注入测试
- XSS 攻击测试

## 7. 部署和上线

### 7.1 部署步骤
1. 数据库脚本执行
2. 配置文件更新
3. 代码部署
4. 短信服务配置
5. 监控告警配置

### 7.2 灰度发布
- 先在测试环境验证功能完整性
- 生产环境小流量灰度测试
- 监控关键指标无异常后全量发布

### 7.3 回滚方案
- 代码回滚：保留原有登录方式不变
- 数据回滚：新增字段不影响原有功能
- 配置回滚：关闭手机号登录功能开关

## 8. 后续优化

### 8.1 功能增强
- 支持国际手机号
- 语音验证码支持
- 生物识别登录
- 社交账号绑定

### 8.2 性能优化
- 验证码缓存预热
- 异步短信发送
- 数据库读写分离
- CDN 加速

### 8.3 用户体验优化
- 智能验证码填充
- 登录状态保持
- 多端同步登录
- 个性化推荐

---

**文档版本：** v1.0  
**创建时间：** 2024年12月19日  
**更新时间：** 2024年12月19日  
**负责人：** 开发团队
