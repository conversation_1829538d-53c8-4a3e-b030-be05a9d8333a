package com.macro.mall.portal.util;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

/**
 * 手机号工具类测试
 * Created by macro on 2025/01/12.
 */
public class PhoneUtilTest {

    @Test
    public void testValidPhone() {
        // 测试有效手机号
        assertTrue(PhoneUtil.isValidPhone("13800138000"));
        assertTrue(PhoneUtil.isValidPhone("18912345678"));
        assertTrue(PhoneUtil.isValidPhone("15999999999"));
        
        // 测试无效手机号
        assertFalse(PhoneUtil.isValidPhone("12800138000")); // 不是1[3-9]开头
        assertFalse(PhoneUtil.isValidPhone("1380013800")); // 少于11位
        assertFalse(PhoneUtil.isValidPhone("138001380000")); // 多于11位
        assertFalse(PhoneUtil.isValidPhone("13800138a00")); // 包含字母
        assertFalse(PhoneUtil.isValidPhone("")); // 空字符串
        assertFalse(PhoneUtil.isValidPhone(null)); // null
        assertFalse(PhoneUtil.isValidPhone("   ")); // 空白字符
    }

    @Test
    public void testMaskPhone() {
        assertEquals("138****8000", PhoneUtil.mask("13800138000"));
        assertEquals("189****5678", PhoneUtil.mask("18912345678"));
        
        // 边界情况
        assertEquals("123", PhoneUtil.mask("123")); // 长度不足7位
        assertEquals(null, PhoneUtil.mask(null)); // null输入
    }

    @Test
    public void testFormatPhone() {
        assertEquals("13800138000", PhoneUtil.format("138-0013-8000"));
        assertEquals("13800138000", PhoneUtil.format("138 0013 8000"));
        assertEquals("13800138000", PhoneUtil.format(" 138 0013 8000 "));
        assertEquals("13800138000", PhoneUtil.format("138.0013.8000"));
        
        // 边界情况
        assertNull(PhoneUtil.format(null)); // null输入
        assertEquals("", PhoneUtil.format("")); // 空字符串
    }
}