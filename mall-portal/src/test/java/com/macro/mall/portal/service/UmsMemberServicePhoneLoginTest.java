package com.macro.mall.portal.service;

import com.macro.mall.mapper.UmsMemberLevelMapper;
import com.macro.mall.mapper.UmsMemberMapper;
import com.macro.mall.model.UmsMember;
import com.macro.mall.model.UmsMemberLevel;
import com.macro.mall.portal.exception.AuthCodeException;
import com.macro.mall.portal.exception.SmsException;
import com.macro.mall.portal.service.impl.UmsMemberServiceImpl;
import com.macro.mall.security.util.JwtTokenUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.security.crypto.password.PasswordEncoder;

import java.util.Arrays;
import java.util.Collections;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * UmsMemberService手机号登录功能测试
 * Created by macro on 2025/01/12.
 */
@ExtendWith(MockitoExtension.class)
public class UmsMemberServicePhoneLoginTest {

    @Mock
    private UmsMemberMapper memberMapper;
    
    @Mock
    private UmsMemberLevelMapper memberLevelMapper;
    
    @Mock
    private UmsMemberCacheService memberCacheService;
    
    @Mock
    private SmsService smsService;
    
    @Mock
    private PasswordEncoder passwordEncoder;
    
    @Mock
    private JwtTokenUtil jwtTokenUtil;
    
    @InjectMocks
    private UmsMemberServiceImpl memberService;

    private String testPhone = "13800138000";
    private String testAuthCode = "123456";
    private String testToken = "test-jwt-token";

    @BeforeEach
    public void setUp() {
        // 设置测试用的配置值
        // 注意：实际使用时这些值应该通过@Value注入
    }

    @Test
    public void testGeneratePhoneAuthCode_Success() {
        // 准备测试数据
        when(smsService.checkSendLimit(testPhone)).thenReturn(true);
        when(smsService.sendAuthCode(eq(testPhone), anyString())).thenReturn(true);
        doNothing().when(memberCacheService).setAuthCode(eq(testPhone), anyString());

        // 执行测试
        String authCode = memberService.generatePhoneAuthCode(testPhone);

        // 验证结果
        assertNotNull(authCode);
        assertEquals(6, authCode.length());
        assertTrue(authCode.matches("\\d{6}")); // 验证是6位数字

        // 验证方法调用
        verify(smsService).checkSendLimit(testPhone);
        verify(smsService).sendAuthCode(eq(testPhone), anyString());
        verify(memberCacheService).setAuthCode(eq(testPhone), anyString());
    }

    @Test
    public void testGeneratePhoneAuthCode_InvalidPhone() {
        // 测试无效手机号
        String invalidPhone = "12345";
        
        // 执行测试并验证异常
        AuthCodeException exception = assertThrows(AuthCodeException.class, () -> {
            memberService.generatePhoneAuthCode(invalidPhone);
        });
        
        assertEquals("手机号格式不正确", exception.getMessage());
    }

    @Test
    public void testGeneratePhoneAuthCode_SendLimitExceeded() {
        // 准备测试数据
        when(smsService.checkSendLimit(testPhone)).thenReturn(false);

        // 执行测试并验证异常
        AuthCodeException exception = assertThrows(AuthCodeException.class, () -> {
            memberService.generatePhoneAuthCode(testPhone);
        });
        
        assertEquals("验证码发送过于频繁，请稍后再试", exception.getMessage());
    }

    @Test
    public void testGeneratePhoneAuthCode_SmsSendFailed() {
        // 准备测试数据
        when(smsService.checkSendLimit(testPhone)).thenReturn(true);
        when(smsService.sendAuthCode(eq(testPhone), anyString())).thenReturn(false);

        // 执行测试并验证异常
        SmsException exception = assertThrows(SmsException.class, () -> {
            memberService.generatePhoneAuthCode(testPhone);
        });
        
        assertEquals("短信发送失败，请稍后重试", exception.getMessage());
    }

    @Test
    public void testLoginByPhone_ExistingUser_Success() {
        // 准备测试数据
        UmsMember existingMember = createTestMember();
        when(memberCacheService.getAuthCode(testPhone)).thenReturn(testAuthCode);
        when(memberCacheService.getMemberByPhone(testPhone)).thenReturn(existingMember);
        when(memberMapper.selectByExample(any())).thenReturn(Arrays.asList(existingMember));
        when(jwtTokenUtil.generateToken(any())).thenReturn(testToken);
        doNothing().when(memberCacheService).delAuthCode(testPhone);

        // 执行测试
        String token = memberService.loginByPhone(testPhone, testAuthCode);

        // 验证结果
        assertEquals(testToken, token);
        
        // 验证方法调用
        verify(memberCacheService).getAuthCode(testPhone);
        verify(memberCacheService).delAuthCode(testPhone);
        verify(jwtTokenUtil).generateToken(any());
    }

    @Test
    public void testLoginByPhone_NewUser_AutoRegister() {
        // 准备测试数据 - 新用户（不存在）
        when(memberCacheService.getAuthCode(testPhone)).thenReturn(testAuthCode);
        when(memberCacheService.getMemberByPhone(testPhone)).thenReturn(null);
        when(memberMapper.selectByExample(any())).thenReturn(Collections.emptyList());
        
        // 设置自动注册相关mock
        UmsMemberLevel defaultLevel = new UmsMemberLevel();
        defaultLevel.setId(1L);
        when(memberLevelMapper.selectByExample(any())).thenReturn(Arrays.asList(defaultLevel));
        when(passwordEncoder.encode(anyString())).thenReturn("encoded-password");
        doNothing().when(memberMapper).insert(any(UmsMember.class));
        doNothing().when(memberCacheService).setMember(any(UmsMember.class));
        when(jwtTokenUtil.generateToken(any())).thenReturn(testToken);
        doNothing().when(memberCacheService).delAuthCode(testPhone);

        // 执行测试
        String token = memberService.loginByPhone(testPhone, testAuthCode);

        // 验证结果
        assertEquals(testToken, token);
        
        // 验证自动注册相关方法被调用
        verify(memberMapper).insert(any(UmsMember.class));
        verify(memberCacheService).setMember(any(UmsMember.class));
    }

    @Test
    public void testLoginByPhone_InvalidAuthCode() {
        // 准备测试数据
        when(memberCacheService.getAuthCode(testPhone)).thenReturn("999999"); // 不同的验证码

        // 执行测试并验证异常
        AuthCodeException exception = assertThrows(AuthCodeException.class, () -> {
            memberService.loginByPhone(testPhone, testAuthCode);
        });
        
        assertEquals("验证码错误或已过期", exception.getMessage());
    }

    @Test
    public void testLoginByPhone_InvalidPhone() {
        String invalidPhone = "12345";
        
        // 执行测试并验证异常
        AuthCodeException exception = assertThrows(AuthCodeException.class, () -> {
            memberService.loginByPhone(invalidPhone, testAuthCode);
        });
        
        assertEquals("手机号格式不正确", exception.getMessage());
    }

    @Test
    public void testGetByPhone_Found() {
        // 准备测试数据
        UmsMember testMember = createTestMember();
        when(memberCacheService.getMemberByPhone(testPhone)).thenReturn(testMember);

        // 执行测试
        UmsMember result = memberService.getByPhone(testPhone);

        // 验证结果
        assertNotNull(result);
        assertEquals(testPhone, result.getPhone());
        assertEquals(testMember.getId(), result.getId());
    }

    @Test
    public void testGetByPhone_NotFound() {
        // 准备测试数据
        when(memberCacheService.getMemberByPhone(testPhone)).thenReturn(null);
        when(memberMapper.selectByExample(any())).thenReturn(Collections.emptyList());

        // 执行测试
        UmsMember result = memberService.getByPhone(testPhone);

        // 验证结果
        assertNull(result);
    }

    private UmsMember createTestMember() {
        UmsMember member = new UmsMember();
        member.setId(1L);
        member.setUsername(testPhone);
        member.setPhone(testPhone);
        member.setPassword("encoded-password");
        member.setStatus(1);
        member.setMemberLevelId(1L);
        return member;
    }
}