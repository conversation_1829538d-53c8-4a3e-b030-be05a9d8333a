package com.macro.mall.portal.util;

import java.util.regex.Pattern;

/**
 * 手机号工具类
 * Created by macro on 2025/01/12.
 */
public class PhoneUtil {
    
    /**
     * 中国大陆手机号正则表达式
     */
    private static final String CHINA_PHONE_REGEX = "^1[3-9]\\d{9}$";
    private static final Pattern CHINA_PHONE_PATTERN = Pattern.compile(CHINA_PHONE_REGEX);
    
    /**
     * 验证手机号格式
     * @param phone 手机号
     * @return 是否有效
     */
    public static boolean isValidPhone(String phone) {
        if (phone == null || phone.trim().isEmpty()) {
            return false;
        }
        return CHINA_PHONE_PATTERN.matcher(phone.trim()).matches();
    }
    
    /**
     * 手机号脱敏处理
     * @param phone 原始手机号
     * @return 脱敏后的手机号
     */
    public static String mask(String phone) {
        if (phone == null || phone.length() < 7) {
            return phone;
        }
        return phone.substring(0, 3) + "****" + phone.substring(7);
    }
    
    /**
     * 格式化手机号（去除空格、横线等）
     * @param phone 原始手机号
     * @return 格式化后的手机号
     */
    public static String format(String phone) {
        if (phone == null) {
            return null;
        }
        return phone.replaceAll("[^0-9]", "");
    }
}