package com.macro.mall.portal.service.impl;

import com.macro.mall.common.service.RedisService;
import com.macro.mall.portal.service.SmsService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * 短信服务实现类
 * Created by macro on 2025/01/12.
 */
@Service
public class SmsServiceImpl implements SmsService {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(SmsServiceImpl.class);
    
    @Autowired
    private RedisService redisService;
    
    @Value("${redis.database}")
    private String REDIS_DATABASE;
    
    // 发送频率限制：60秒内只能发送1次
    private static final long SEND_INTERVAL = 60;
    
    @Override
    public boolean sendAuthCode(String telephone, String authCode) {
        try {
            // 这里应该调用真实的短信服务商API发送短信
            // 为了演示，我们模拟发送成功
            LOGGER.info("发送短信验证码到手机号: {}, 验证码: {}", maskPhone(telephone), "****");
            
            // 模拟发送延迟
            Thread.sleep(100);
            
            // 记录发送时间，用于频率控制
            recordSendTime(telephone);
            
            return true;
        } catch (Exception e) {
            LOGGER.error("短信发送失败, 手机号: {}, 错误: {}", maskPhone(telephone), e.getMessage());
            return false;
        }
    }
    
    @Override
    public boolean checkSendLimit(String telephone) {
        String key = REDIS_DATABASE + ":sms:send_time:" + telephone;
        Object lastSendTimeObj = redisService.get(key);
        
        if (lastSendTimeObj == null) {
            return true; // 没有发送记录，可以发送
        }
        
        long lastSendTime = (Long) lastSendTimeObj;
        long currentTime = System.currentTimeMillis() / 1000;
        
        return (currentTime - lastSendTime) >= SEND_INTERVAL;
    }
    
    /**
     * 记录发送时间
     */
    private void recordSendTime(String telephone) {
        String key = REDIS_DATABASE + ":sms:send_time:" + telephone;
        long currentTime = System.currentTimeMillis() / 1000;
        // 设置过期时间为发送间隔时间
        redisService.set(key, currentTime, SEND_INTERVAL);
    }
    
    /**
     * 手机号脱敏
     */
    private String maskPhone(String telephone) {
        if (telephone == null || telephone.length() < 7) {
            return telephone;
        }
        return telephone.substring(0, 3) + "****" + telephone.substring(7);
    }
}