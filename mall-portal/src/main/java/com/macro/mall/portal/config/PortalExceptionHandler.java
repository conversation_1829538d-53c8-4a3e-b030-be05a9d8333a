package com.macro.mall.portal.config;

import com.macro.mall.common.api.CommonResult;
import com.macro.mall.portal.exception.AuthCodeException;
import com.macro.mall.portal.exception.SmsException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * Portal模块全局异常处理器
 * Created by macro on 2025/01/12.
 */
@ControllerAdvice
public class PortalExceptionHandler {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(PortalExceptionHandler.class);
    
    @ResponseBody
    @ExceptionHandler(value = SmsException.class)
    public CommonResult handleSmsException(SmsException e) {
        LOGGER.warn("短信服务异常: {}", e.getMessage());
        return CommonResult.failed(e.getMessage());
    }

    @ResponseBody
    @ExceptionHandler(value = AuthCodeException.class) 
    public CommonResult handleAuthCodeException(AuthCodeException e) {
        LOGGER.warn("验证码异常: {}", e.getMessage());
        return CommonResult.validateFailed(e.getMessage());
    }
}