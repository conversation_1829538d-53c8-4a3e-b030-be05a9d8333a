package com.macro.mall.portal.service;

/**
 * 短信服务接口
 * Created by macro on 2025/01/12.
 */
public interface SmsService {
    
    /**
     * 发送短信验证码
     * @param telephone 手机号
     * @param authCode 验证码
     * @return 发送结果
     */
    boolean sendAuthCode(String telephone, String authCode);
    
    /**
     * 检查手机号发送频率限制
     * @param telephone 手机号
     * @return true=可以发送, false=频率受限
     */
    boolean checkSendLimit(String telephone);
}