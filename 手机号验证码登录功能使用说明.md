# 手机号验证码登录功能使用说明

## 功能概述

本功能实现了基于手机号+验证码的登录方式，当手机号未注册时会自动创建新用户账号。该功能基于现有的Spring Security + JWT认证架构，完全兼容原有的登录体系。

## 核心特性

1. **手机号验证码登录**：用户使用手机号和验证码即可登录
2. **自动注册机制**：未注册的手机号首次登录时自动创建账号
3. **安全防护**：验证码5分钟有效期，发送频率限制
4. **完整集成**：与现有JWT认证体系无缝集成

## 接口说明

### 1. 获取手机验证码

**接口地址**：`GET /sso/getPhoneAuthCode`

**请求参数**：
- `telephone`（必填）：手机号，格式如 13800138000

**请求示例**：
```
GET /sso/getPhoneAuthCode?telephone=13800138000
```

**响应示例**：
```json
{
    "code": 200,
    "message": "验证码发送成功",
    "data": null
}
```

### 2. 手机号验证码登录

**接口地址**：`POST /sso/phoneLogin`

**请求参数**：
- `telephone`（必填）：手机号
- `authCode`（必填）：6位验证码

**请求示例**：
```
POST /sso/phoneLogin
Content-Type: application/x-www-form-urlencoded

telephone=13800138000&authCode=123456
```

**响应示例**：
```json
{
    "code": 200,
    "message": "登录成功",
    "data": {
        "token": "eyJhbGciOiJIUzUxMiJ9...",
        "tokenHead": "Bearer ",
        "member": {
            "id": 1,
            "username": "13800138000",
            "phone": "13800138000",
            "status": 1
        }
    }
}
```

## 使用流程

### 1. 标准登录流程

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant API as 后端API
    participant SMS as 短信服务
    participant Cache as Redis缓存
    participant DB as 数据库

    Client->>API: 1. 获取验证码
    API->>SMS: 2. 发送短信
    API->>Cache: 3. 存储验证码
    API-->>Client: 4. 发送成功
    
    Client->>API: 5. 验证码登录
    API->>Cache: 6. 验证验证码
    API->>DB: 7. 查询/创建用户
    API-->>Client: 8. 返回Token
```

### 2. 自动注册流程

当手机号首次登录时：
1. 验证验证码正确性
2. 根据手机号查询用户，发现不存在
3. 自动创建新用户：
   - 用户名设为手机号
   - 设置默认密码（123456）
   - 分配默认会员等级
   - 账户状态设为启用
4. 生成JWT Token返回

## 安全机制

### 1. 验证码安全
- **有效期**：5分钟自动过期
- **长度**：6位随机数字
- **一次性使用**：验证成功后立即删除
- **存储**：Redis缓存，提高性能

### 2. 防刷机制
- **发送频率限制**：同一手机号60秒内只能发送1次验证码
- **格式验证**：严格验证手机号格式（支持中国大陆号码）
- **异常处理**：完善的错误处理和日志记录

### 3. 数据安全
- **手机号脱敏**：日志中手机号显示为 138****8000
- **密码加密**：自动注册用户的密码使用BCrypt加密
- **参数验证**：严格的输入参数格式验证

## 错误处理

| 错误情况 | HTTP状态码 | 错误信息 |
|----------|------------|----------|
| 手机号格式错误 | 400 | 手机号格式不正确 |
| 验证码为空 | 400 | 验证码不能为空 |
| 验证码错误 | 400 | 验证码错误或已过期 |
| 发送频率限制 | 400 | 验证码发送过于频繁，请稍后再试 |
| 短信发送失败 | 500 | 短信发送失败，请稍后重试 |

## 技术实现

### 1. 核心组件

#### 新增文件：
- `SmsService` - 短信服务接口
- `SmsServiceImpl` - 短信服务实现
- `PhoneUtil` - 手机号工具类
- `AuthCodeException` - 验证码异常类
- `SmsException` - 短信异常类
- `PortalExceptionHandler` - 异常处理器

#### 扩展文件：
- `UmsMemberService` - 增加手机号登录方法
- `UmsMemberServiceImpl` - 实现手机号登录逻辑
- `UmsMemberCacheService` - 增加手机号缓存方法
- `UmsMemberController` - 增加相关API接口

### 2. 关键方法

- `generatePhoneAuthCode(telephone)` - 生成并发送验证码
- `loginByPhone(telephone, authCode)` - 手机号验证码登录
- `getByPhone(telephone)` - 根据手机号获取用户
- `autoRegisterByPhone(telephone)` - 自动注册新用户

## 配置说明

需要在application.yml中配置以下参数：

```yaml
# Redis配置（现有）
redis:
  database: mall
  key:
    authCode: ums:authCode
    member: ums:member
  expire:
    authCode: 300  # 验证码过期时间（秒）
    common: 86400  # 用户缓存过期时间（秒）

# 短信服务配置（新增）
sms:
  provider: mock  # 短信服务商（当前为模拟）
  send-interval: 60  # 发送间隔（秒）
```

## 测试说明

### 1. 单元测试
已提供完整的单元测试：
- `PhoneUtilTest` - 手机号工具类测试
- `UmsMemberServicePhoneLoginTest` - 手机号登录功能测试

### 2. 接口测试
可以使用Postman等工具进行接口测试：

1. **获取验证码**：
   ```
   GET http://localhost:8085/sso/getPhoneAuthCode?telephone=13800138000
   ```

2. **验证码登录**：
   ```
   POST http://localhost:8085/sso/phoneLogin
   Content-Type: application/x-www-form-urlencoded
   
   telephone=13800138000&authCode=123456
   ```

## 注意事项

1. **短信服务集成**：当前使用模拟短信发送，生产环境需要集成真实的短信服务商（如阿里云、腾讯云等）

2. **验证码查看**：开发测试时，验证码会在日志中显示（生产环境应移除）

3. **默认密码**：自动注册用户的默认密码为"123456"，建议引导用户首次登录后修改密码

4. **兼容性**：该功能完全兼容现有的用户名密码登录方式

5. **缓存依赖**：功能依赖Redis缓存，请确保Redis服务正常运行

## 扩展建议

1. **短信模板**：配置不同场景的短信模板
2. **多重验证**：结合图形验证码等多重验证手段
3. **监控告警**：添加短信发送量监控和异常告警
4. **国际化**：支持国际手机号码格式
5. **用户引导**：首次自动注册用户的欢迎流程

## 技术支持

如有技术问题，请参考：
1. 查看应用日志获取详细错误信息
2. 检查Redis连接状态
3. 验证配置参数是否正确
4. 确认数据库表结构完整性