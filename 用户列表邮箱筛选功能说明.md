# 用户列表邮箱筛选功能使用说明

## 功能概述

已为后台管理系统的用户列表功能添加了按邮箱筛选的支持。现在可以根据用户名、昵称或邮箱来筛选用户列表。

## API 接口

### 获取用户列表 

**接口地址：** `GET /admin/list`

**参数说明：**

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|---------|------|
| keyword | String | 否 | - | 根据用户名或昵称进行模糊搜索 |
| email | String | 否 | - | 根据邮箱进行模糊搜索 |
| pageSize | Integer | 否 | 5 | 每页显示数量 |
| pageNum | Integer | 否 | 1 | 页码 |

**使用示例：**

1. **仅按邮箱筛选：**
   ```
   GET /admin/list?email=gmail.com&pageSize=10&pageNum=1
   ```
   
2. **按关键字和邮箱同时筛选：**
   ```
   GET /admin/list?keyword=admin&email=example.com&pageSize=10&pageNum=1
   ```

3. **仅按关键字筛选（原有功能）：**
   ```
   GET /admin/list?keyword=admin&pageSize=10&pageNum=1
   ```

4. **获取所有用户（无筛选）：**
   ```
   GET /admin/list?pageSize=10&pageNum=1
   ```

## 筛选逻辑

### 单一条件筛选
- **仅keyword参数：** 返回用户名或昵称包含关键字的用户
- **仅email参数：** 返回邮箱包含指定内容的用户

### 组合条件筛选
- **keyword + email：** 返回同时满足以下条件的用户：
  - (用户名包含keyword OR 昵称包含keyword) AND 邮箱包含email

### 模糊匹配
- 所有文本筛选都采用模糊匹配（LIKE '%关键字%'）
- 大小写敏感（根据数据库配置）

## 响应格式

```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "pageNum": 1,
        "pageSize": 5,
        "totalPage": 2,
        "total": 8,
        "list": [
            {
                "id": 1,
                "username": "admin",
                "email": "<EMAIL>",
                "nickName": "管理员",
                "status": 1,
                "createTime": "2023-01-01T00:00:00",
                "loginTime": "2023-01-02T00:00:00",
                "icon": "http://example.com/avatar.jpg",
                "note": "系统管理员"
            }
        ]
    }
}
```

## 代码变更说明

### 修改的文件：

1. **UmsAdminController.java**
   - 添加了 `email` 参数
   - 更新了 API 文档注释
   - 添加了参数说明注释

2. **UmsAdminService.java**
   - 更新了 `list` 方法签名，添加 `email` 参数

3. **UmsAdminServiceImpl.java**
   - 实现了邮箱筛选逻辑
   - 支持关键字和邮箱的组合筛选
   - 保持向后兼容性

### 新增的文件：

1. **UmsAdminServiceEmailFilterTest.java**
   - 邮箱筛选功能的单元测试
   - 包含多种筛选场景的测试用例

## 向后兼容性

- 所有现有的API调用都保持兼容
- 新的 `email` 参数是可选的
- 不传 `email` 参数时，功能与之前完全一致

## 注意事项

1. 邮箱筛选采用模糊匹配，可以搜索邮箱的任意部分
2. 组合筛选时，两个条件都必须满足
3. 分页功能正常工作，可以在筛选结果中进行分页
4. 建议在邮箱字段上添加数据库索引以提升查询性能

## 测试建议

在使用前，建议进行以下测试：

1. 验证仅邮箱筛选功能
2. 验证关键字和邮箱组合筛选
3. 验证空筛选条件的处理
4. 验证不存在的邮箱筛选结果
5. 验证分页功能在筛选结果中的正常工作