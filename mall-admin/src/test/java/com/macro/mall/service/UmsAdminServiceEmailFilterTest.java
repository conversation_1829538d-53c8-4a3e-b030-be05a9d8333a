package com.macro.mall.service;

import com.macro.mall.model.UmsAdmin;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.List;

/**
 * 用户管理服务邮箱筛选功能测试
 * Created for email filter functionality testing
 */
@SpringBootTest
@ActiveProfiles("test")
public class UmsAdminServiceEmailFilterTest {

    @Autowired
    private UmsAdminService umsAdminService;

    @Test
    public void testListWithEmailFilter() {
        // 测试仅按邮箱筛选
        List<UmsAdmin> admins = umsAdminService.list(null, "<EMAIL>", 10, 1);
        System.out.println("按邮箱筛选结果数量: " + admins.size());
        
        // 验证结果中的邮箱都包含筛选条件
        for (UmsAdmin admin : admins) {
            if (admin.getEmail() != null) {
                assert admin.getEmail().contains("<EMAIL>") : 
                    "邮箱筛选失败，发现不匹配的邮箱: " + admin.getEmail();
            }
        }
    }

    @Test
    public void testListWithKeywordAndEmailFilter() {
        // 测试同时按关键字和邮箱筛选
        List<UmsAdmin> admins = umsAdminService.list("admin", "gmail.com", 10, 1);
        System.out.println("按关键字和邮箱筛选结果数量: " + admins.size());
        
        // 验证结果符合筛选条件
        for (UmsAdmin admin : admins) {
            boolean matchesKeyword = (admin.getUsername() != null && admin.getUsername().contains("admin")) ||
                                   (admin.getNickName() != null && admin.getNickName().contains("admin"));
            boolean matchesEmail = admin.getEmail() != null && admin.getEmail().contains("gmail.com");
            
            assert matchesKeyword && matchesEmail : 
                "筛选条件不匹配，用户名: " + admin.getUsername() + ", 昵称: " + admin.getNickName() + ", 邮箱: " + admin.getEmail();
        }
    }

    @Test
    public void testListWithEmptyFilters() {
        // 测试空筛选条件（应该返回所有用户）
        List<UmsAdmin> admins = umsAdminService.list(null, null, 10, 1);
        System.out.println("无筛选条件结果数量: " + admins.size());
        
        // 应该返回一些结果（假设数据库中有用户）
        assert admins.size() >= 0 : "无筛选条件应该返回所有用户";
    }

    @Test
    public void testListWithNonExistentEmail() {
        // 测试不存在的邮箱
        List<UmsAdmin> admins = umsAdminService.list(null, "<EMAIL>", 10, 1);
        System.out.println("不存在邮箱筛选结果数量: " + admins.size());
        
        // 应该返回空结果
        assert admins.size() == 0 : "不存在的邮箱应该返回空结果";
    }
}